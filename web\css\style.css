;@font-face {
    font-family: 'Library3am';
    src: url('./fonts/Library3am.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: transparent;
    overflow: hidden;
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
}

/* Černé pozadí pro development na webu */
body.development {
    background: #000000;
}

/* Na webu - telefon vpravo dole když je skrytý */
#phone-container.development.phone-hidden {
    position: fixed;
    bottom: 20px;
    right: 20px;
    transform: none;
    top: auto;
    left: auto;
}

/* Na webu - telefon vycentrovaný když je otevřený */
#phone-container.development.phone-visible {
    position: fixed;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    bottom: auto;
    right: auto;
}

#phone-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    transform: none;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: auto;
    z-index: 9999;
}

#phone-container:hover {
    cursor: pointer;
}

.phone-hidden {
    display: none !important;
    pointer-events: none;
}

.phone-visible {
    display: block !important;
    transform: translateY(0);
    animation: slideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    pointer-events: auto;
}

.phone-closing {
    display: block;
    animation: slideDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideDown {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(100%);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.phone {
    width: 320px;
    height: 640px;
    background: linear-gradient(145deg, #1d1d1f, #2c2c2e);
    border-radius: 50px;
    padding: 8px;
    position: relative;
    box-shadow: 
        0 25px 50px rgba(0,0,0,0.5),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.2);
    border: 2px solid #3a3a3c;
}

/* Tlačítko hlasitosti nahoru */
.phone .volume-up {
    position: absolute;
    left: -4px;
    top: 120px;
    width: 6px;
    height: 30px;
    background: linear-gradient(90deg, #2c2c2e, #1d1d1f);
    border-radius: 3px 0 0 3px;
    border: 1px solid #3a3a3c;
    border-right: none;
}

/* Tlačítko hlasitosti dolů */
.phone .volume-down {
    position: absolute;
    left: -4px;
    top: 160px;
    width: 6px;
    height: 30px;
    background: linear-gradient(90deg, #2c2c2e, #1d1d1f);
    border-radius: 3px 0 0 3px;
    border: 1px solid #3a3a3c;
    border-right: none;
}

/* Power button */
.phone .power-button {
    position: absolute;
    right: -4px;
    top: 140px;
    width: 6px;
    height: 50px;
    background: linear-gradient(270deg, #2c2c2e, #1d1d1f);
    border-radius: 0 3px 3px 0;
    border: 1px solid #3a3a3c;
    border-left: none;
}

.phone::before {
    content: '';
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-40px);
    width: 70px;
    height: 25px;
    background: #000000;
    border-radius: 15px;
    z-index: 1001;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.3);
}

.phone::after {
    content: '';
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(15px);
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, #0a0a0a 20%, #333 60%, #111 100%);
    border-radius: 50%;
    z-index: 1002;
    box-shadow: 
        inset 0 1px 1px rgba(0,0,0,0.9),
        inset 0 -1px 1px rgba(255,255,255,0.05),
        0 0 2px rgba(0,0,0,0.5);
}

.phone-screen {
    width: 100%;
    height: 624px;
    background: #ffffff;
    border-radius: 42px;
    overflow: hidden;
    position: relative;
}

.status-bar {
    height: 25px;
    background: transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 25px;
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
    font-size: 13px;
    font-weight: 600;
    position: absolute;
    top: 23px;
    left: 0;
    right: 0;
    z-index: 100;
}

.language-screen .status-bar {
    color: #000000 !important;
}

.language-screen .status-bar .time,
.language-screen .status-bar #current-time {
    color: #000000 !important;
}

.language-screen .status-bar .status-icons i {
    color: #000000 !important;
}

.language-screen .status-bar {
    color: #000000 !important;
}

.language-screen .status-bar .time {
    color: #000000 !important;
}

.language-screen .status-bar .status-icons i {
    color: #000000 !important;
}

.status-icons i {
    margin-left: 5px;
}

.home-screen {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60px 20px 30px 20px;
}

.apps-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 50px;
}

.app {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.app:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.05);
}

.app i {
    font-size: 24px;
    color: white;
    margin-bottom: 8px;
}

.app span {
    color: white;
    font-size: 11px;
    text-align: center;
}

.app-screen {
    height: 100%;
    background: #f5f5f5;
    padding: 60px 20px 30px 20px;
}

.app-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.back-btn {
    font-size: 18px;
    color: #007AFF;
    cursor: pointer;
    margin-right: 15px;
}

.app-header h2 {
    color: #333;
    font-size: 18px;
}

.dialer input {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 10px;
    font-size: 16px;
    margin-bottom: 15px;
}

.dialer button {
    width: 100%;
    padding: 15px;
    background: #007AFF;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    cursor: pointer;
}

.message-compose input,
.message-compose textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 10px;
    font-size: 14px;
    margin-bottom: 15px;
}

.message-compose textarea {
    height: 100px;
    resize: none;
}

.message-compose button {
    width: 100%;
    padding: 15px;
    background: #34C759;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    cursor: pointer;
}

.home-button {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 5px;
    background: rgba(255,255,255,0.6);
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.home-button:hover {
    background: rgba(255,255,255,0.8);
    transform: translateX(-50%) scale(1.05);
}

.home-button.hidden {
    display: none;
}

.home-indicator {
    display: none;
}

.setup-screen {
    height: 100%;
    background: url('../img/bg.png'), linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover, cover;
    background-position: 70% center, center;
    background-repeat: no-repeat, no-repeat;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    text-align: center;
    padding: 40px 20px;
    position: relative;
    overflow: hidden;
    transition: transform 0.5s ease-out;
    z-index: 10;
}

.setup-screen.swiping-up {
    transform: translateY(-100%);
}

.setup-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
}

.bg-circle-1 {
    width: 200px;
    height: 200px;
    top: -50px;
    left: -50px;
    animation: float 25s ease-in-out infinite;
}

.bg-circle-2 {
    width: 150px;
    height: 150px;
    top: 20%;
    right: -30px;
    animation: float 20s ease-in-out infinite reverse;
}

.bg-circle-3 {
    width: 100px;
    height: 100px;
    bottom: 30%;
    left: 20px;
    animation: float 18s ease-in-out infinite;
}

.bg-circle-4 {
    width: 80px;
    height: 80px;
    bottom: -20px;
    right: 30%;
    animation: float 22s ease-in-out infinite reverse;
}

.bg-line {
    position: absolute;
    background: linear-gradient(45deg, rgba(255,255,255,0.08) 0%, transparent 100%);
    border-radius: 50px;
}

.bg-line-1 {
    width: 300px;
    height: 4px;
    top: 25%;
    left: -100px;
    transform: rotate(45deg);
    animation: slide 30s linear infinite;
}

.bg-line-2 {
    width: 200px;
    height: 3px;
    top: 60%;
    right: -80px;
    transform: rotate(-30deg);
    animation: slide 25s linear infinite reverse;
}

.bg-line-3 {
    width: 150px;
    height: 2px;
    bottom: 20%;
    left: -50px;
    transform: rotate(60deg);
    animation: slide 35s linear infinite;
}

.bg-wave {
    position: absolute;
    width: 400px;
    height: 400px;
    border: 2px solid rgba(255,255,255,0.05);
    border-radius: 50%;
}

.bg-wave-1 {
    top: -150px;
    right: -150px;
    animation: wave 40s linear infinite;
}

.bg-wave-2 {
    bottom: -200px;
    left: -200px;
    animation: wave 35s linear infinite reverse;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-30px) rotate(10deg);
    }
}

@keyframes slide {
    0% {
        transform: translateX(-100px) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100px) rotate(45deg);
        opacity: 0;
    }
}

@keyframes wave {
    0% {
        transform: scale(0.8) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 0.1;
    }
    100% {
        transform: scale(0.8) rotate(360deg);
        opacity: 0.3;
    }
}

.hello-text {
    font-family: 'Fredoka One', 'Nunito', 'Comfortaa', 'Quicksand', sans-serif;
    font-size: 90px;
    font-weight: 900;
    margin-bottom: 60px;
    background: linear-gradient(45deg,
        #ff0066, #ff6600, #ffcc00, #66ff00, #0066ff, #6600ff, #ff0066);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 5px;
    opacity: 0;
    overflow: hidden;
    white-space: nowrap;
    width: 0;
    animation: typeAndShow 3s ease-out forwards, gradientShift 1.5s linear infinite;
    text-shadow: 0 0 50px rgba(255, 0, 102, 0.8);
    position: relative;
    z-index: 10;
    filter: drop-shadow(0 0 50px rgba(255, 0, 102, 0.8)) drop-shadow(0 0 100px rgba(102, 0, 255, 0.4));
    text-transform: lowercase;
    -webkit-text-stroke: 3px rgba(255, 255, 255, 0.1);
    border-radius: 20px;
}

@keyframes typeAndShow {
    0% {
        opacity: 1;
        width: 0;
    }
    100% {
        width: 100%;
        opacity: 1;
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 200% 50%;
    }
}

@keyframes textPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes drawHello {
    from {
        stroke-dashoffset: 1000;
    }
    to {
        stroke-dashoffset: 0;
    }
}

@keyframes typewriter {
    0% {
        opacity: 1;
        width: 0;
        border-right: 2px solid white;
    }
    50% {
        width: 100%;
        border-right: 2px solid white;
    }
    100% {
        width: 100%;
        border-right: none;
        opacity: 1;
    }
}

.swipe-area {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0;
    animation: showSwipeArea 0.5s ease-out 3s forwards;
    z-index: 10;
}

@keyframes showSwipeArea {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.swipe-text {
    font-family: 'Comic Sans MS', 'Chalkduster', fantasy;
    font-size: 16px;
    font-weight: 600;
    color: rgba(255,255,255,1);
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.swipe-indicator {
    width: 80px;
    height: 4px;
    background: rgba(255,255,255,0.9);
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
    box-shadow: 0 0 15px rgba(255,255,255,0.6);
}

.swipe-indicator:hover {
    background: rgba(255,255,255,0.8);
    transform: translateY(-2px);
    box-shadow: 0 0 15px rgba(255,255,255,0.5);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.8;
    }
}

.language-screen {
    height: 100%;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #000;
    text-align: center;
    padding: 40px 20px;
    padding-top: 70px;
    transition: transform 0.5s ease-out;
    position: relative;
    z-index: 5;
}

.language-screen .status-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    color: #000000 !important;
    font-family: 'Arial Black', 'Arial', sans-serif;
    font-size: 12px;
    font-weight: 700;
    z-index: 100;
}

.language-screen .status-bar .time {
    color: #000000 !important;
}

.language-screen .status-bar .status-icons {
    color: #000000 !important;
}

.language-screen .status-bar .status-icons i {
    color: #000000 !important;
}

.language-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.language-icon svg {
    width: 100%;
    height: 100%;
}

.language-icon path {
    fill: #3b82f6;
    clip-path: circle(0% at 50% 50%);
    animation: revealIcon 4s ease-out forwards;
}

@keyframes revealIcon {
    to {
        clip-path: circle(100% at 50% 50%);
    }
}

@keyframes drawIcon {
    to {
        stroke-dashoffset: 0;
    }
}

.language-title {
    font-family: 'Comic Sans MS', 'Chalkduster', fantasy;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 40px;
    animation: fadeInUp 0.8s ease-out 4.2s forwards;
    opacity: 0;
    color: #000;
    text-shadow: none;
}

.language-options {
    display: flex;
    flex-direction: column;
    gap: 0;
    width: 100%;
    max-width: 280px;
    max-height: 400px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow-y: auto;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    scrollbar-width: none;
}

.language-options::-webkit-scrollbar {
    display: none;
}

.language-option {
    padding: 18px 24px;
    background: #ffffff;
    border: none;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 0;
    color: #000;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.language-option:last-child {
    border-bottom: none;
}

.language-option::after {
    content: '';
    width: 16px;
    height: 16px;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMjQgMjQiPjxwYXRoIGZpbGw9Im5vbmUiIHN0cm9rZT0icmdiYSgwLDAsMCwwLjQpIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMi41IiBkPSJtMTAgMTdsNS01bTAgMGwtNS01Ii8+PC9zdmc+') no-repeat center;
    background-size: contain;
    transition: all 0.3s ease;
    opacity: 0.6;
}

.language-option:hover {
    background: #f5f5f5;
}

.language-option:hover::after {
    opacity: 1;
    transform: translateX(3px);
}

.passcode-screen {
    height: 100%;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #000000;
    text-align: center;
    padding: 60px 20px 0 20px;
    position: relative;
    overflow: hidden;
}

.passcode-screen::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
    animation: backgroundPulse 8s ease-in-out infinite;
    z-index: 0;
}

@keyframes backgroundPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 0.8;
    }
}

.back-button {
    position: absolute;
    top: 60px;
    left: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #007AFF;
    font-size: 16px;
    font-weight: 400;
    transition: all 0.2s ease;
    z-index: 10;
}

.back-button:hover {
    opacity: 0.7;
    transform: translateX(-2px);
}

.back-button svg {
    width: 16px;
    height: 16px;
    color: #007AFF;
}

.passcode-lock-icon {
    margin-bottom: 15px;
    margin-top: 80px;
    opacity: 0;
    animation: lockAppear 1s ease-out forwards;
    filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lock-container {
    position: relative;
    width: 60px;
    height: 70px;
}

.lock-shackle {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 25px;
    border: 4px solid #3b82f6;
    border-bottom: none;
    border-radius: 20px 20px 0 0;
    animation: lockShackleOpen 2s ease-in-out 2s forwards;
    transform-origin: left bottom;
}

.lock-body {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.lock-keyhole {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: #ffffff;
    border-radius: 50%;
    opacity: 0;
    animation: keyholeAppear 0.5s ease-out 1.5s forwards;
}

.lock-keyhole::after {
    content: '';
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    width: 3px;
    height: 8px;
    background: #ffffff;
    border-radius: 0 0 2px 2px;
}

.passcode-title, .passcode-subtitle, .passcode-dots {
    position: relative;
    z-index: 1;
}

.passcode-keypad {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 18px;
    max-width: none;
    width: calc(100% + 40px);
    margin: 0 -20px;
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
    padding: 25px 20px 65px 20px;
    border-radius: 20px 20px 0 0;
    box-shadow: 0 -4px 20px rgba(0,0,0,0.1), 0 -1px 0 rgba(255,255,255,0.8);
    border: 1px solid rgba(255,255,255,0.3);
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-top: 1px solid rgba(203,213,225,0.5);
    transform: translateY(-6px);
    backdrop-filter: blur(10px);
    animation: slideUpKeypad 0.8s ease-out 1.8s forwards;
    opacity: 0;
}

@keyframes lockAppear {
    from {
        opacity: 0;
        transform: scale(0.5) rotate(-10deg);
    }
    to {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

@keyframes lockShackleOpen {
    0% {
        transform: translateX(-50%) rotate(0deg);
    }
    50% {
        transform: translateX(-50%) rotate(-25deg);
    }
    100% {
        transform: translateX(-50%) rotate(-45deg);
    }
}

@keyframes keyholeAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes slideUpKeypad {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(-6px);
    }
}

@keyframes dotFill {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
    100% {
        transform: scale(1.1);
    }
}

@keyframes keyPress {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
        box-shadow: 0 2px 6px rgba(0,0,0,0.2) inset;
    }
    100% {
        transform: scale(1);
    }
}

.passcode-title {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 12px;
    animation: fadeInUp 0.8s ease-out 1.2s forwards;
    opacity: 0;
    color: #1e293b;
    letter-spacing: 0.5px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.passcode-subtitle {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 35px;
    animation: fadeInUp 0.8s ease-out 1.4s forwards;
    opacity: 0;
    color: #64748b;
    line-height: 1.5;
    max-width: 300px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.passcode-dots {
    display: flex;
    gap: 18px;
    margin-bottom: 15px;
    margin-top: -20px;
    animation: fadeInUp 0.8s ease-out 1.6s forwards;
    opacity: 0;
}

.passcode-dot {
    width: 16px;
    height: 16px;
    border: 2px solid #cbd5e1;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
}

.passcode-dot.filled {
    background: #3b82f6;
    border-color: #3b82f6;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    animation: dotFill 0.3s ease-out;
}

.passcode-dot.filled::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: dotShine 0.5s ease-out 0.1s forwards;
}

@keyframes dotShine {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1);
    }
}

.passcode-keypad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 18px;
    max-width: none;
    width: calc(100% + 40px);
    margin: 0 -20px;
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
    padding: 25px 20px 65px 20px;
    border-radius: 20px 20px 0 0;
    box-shadow: 0 -4px 20px rgba(0,0,0,0.1), 0 -1px 0 rgba(255,255,255,0.8);
    border: 1px solid rgba(255,255,255,0.3);
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-top: 1px solid rgba(203,213,225,0.5);
    transform: translateY(-6px);
    backdrop-filter: blur(10px);
    animation: slideUpKeypad 0.8s ease-out 1.8s forwards;
    opacity: 0;
}

.passcode-key {
    width: 70px;
    height: 55px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #1e293b;
    font-size: 26px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1), 0 1px 0 rgba(255,255,255,0.8);
    border: 1px solid rgba(203,213,225,0.3);
    position: relative;
    overflow: hidden;
}

.passcode-key::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.passcode-key:active::before {
    width: 100px;
    height: 100px;
    transition: all 0.1s ease;
}

.passcode-key:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    transform: scale(1.08);
    box-shadow: 0 6px 16px rgba(0,0,0,0.15), 0 2px 0 rgba(255,255,255,0.9);
}

.passcode-key:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2) inset;
}

.passcode-key.delete {
    background: transparent;
    border: none;
    font-size: 20px;
    box-shadow: none;
}

.passcode-key.delete[data-key="delete"] {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #dc2626;
    font-size: 18px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2), 0 1px 0 rgba(255,255,255,0.8);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

.passcode-key.delete[data-key="delete"]:hover {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    transform: scale(1.08);
    box-shadow: 0 6px 16px rgba(220, 38, 38, 0.3), 0 2px 0 rgba(255,255,255,0.9);
}

/* Force black color for language screen status bar */
.language-screen .status-bar * {
    color: #000000 !important;
    text-shadow: none !important;
}

.language-screen #current-time {
    color: #000000 !important;
    text-shadow: none !important;
}